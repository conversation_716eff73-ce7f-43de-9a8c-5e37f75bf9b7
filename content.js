(function() {
  // Function to create and display the popup
  // http://localhost:5000/careplan-on-extension

  if (document.getElementById('extension-popup')) {
      console.log('Popup already exists.');
      return;
  }

  function createPopup() {
    console.log('Creating popup...');

    // Create the popup div
    const popup = document.createElement('div');
    popup.id = 'extension-popup';
    popup.style.position = 'fixed';
    popup.style.top = '0px';
    popup.style.right = '0';
    popup.style.width = '500px';
    popup.style.height = '100vh';
    popup.style.backgroundColor = '#fff';
    popup.style.zIndex = '1000';
    popup.style.transition = "right 0.3s";
    popup.style.overflow = "visible";
    popup.innerHTML = `
        <button id="close-popup" style="cursor: pointer;width: 38px;height: 38px;position: absolute;top: 6px;left: -19px;box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.2);border-radius: 50%;background-color: #fff;border:0;">
          <img src="https://websitestaginbucket.storage.googleapis.com/wp-content/uploads/2024/08/02161037/close.png" style="width: 100%;height: auto;">
        </button>
        <iframe id="popup-iframe" style="width:100%;height: 100%;box-shadow: -2px 0px 10px 0px rgba(0,0,0,0.25);" src="http://127.0.0.1:5500/index.html" frameborder="0"></iframe>
    `;

    // Append the popup to the body
    document.body.appendChild(popup);
    console.log('Popup added to the body.');

    // Add event listener to the close button
    document.getElementById('close-popup').addEventListener('click', function(e) {
      e.preventDefault();
      popup.style.right = '-500px';
      document.getElementById('close-popup').style.display = "none";
      document.getElementById('open-popup').style.display = "flex";
    });

    function findCompanyName() {
      // This detects patient account number from the user opened tab of EMR
      // and sends the value for patient account number to the iframe app.
      const iframe = document.getElementById('popup-iframe');
      let companyName = null;
    
      iframe.addEventListener('load', function () {
        const intervalId = setInterval(function () {
          let uri_curr = document.URL;
          
          if (uri_curr.includes('champlain')) {
            // Only to work on urls like nysucc, which is our EMR
            try {
              // Access the specific iframe that contains the input
              const targetIframe = document.getElementById("WebResource_AccountOfficeProfile");

              if (targetIframe) {
                // Try to access the iframe content (same-origin)
                const iframeDocument = targetIframe.contentDocument || targetIframe.contentWindow.document;
                const nameInput = iframeDocument.getElementById("plus_name");

                console.log('Target iframe found:', targetIframe);
                console.log('Name input:', nameInput);

                if (nameInput) {
                  const companyNameFound = nameInput.value;
                  console.log('Company name found:', companyNameFound)

                  if (companyNameFound) {
                    companyName = companyNameFound;
                    console.log('Sending to popup iframe:', iframe.contentWindow);
                    if (iframe.contentWindow) {
                      iframe.contentWindow.postMessage({ companyName }, '*');
                    }
                    clearInterval(intervalId);
                  }
                }
              } else {
                console.log('Target iframe "WebResource_AccountOfficeProfile" not found');
              }
            } catch (error) {
              console.log('Cross-origin iframe detected or access error:', error);
              // For cross-origin iframes, we might need alternative approaches
            }
          } else {
            console.log(`>> interacting with non-ecw pages << no acct no will be sent to app`);
          }
        }, 500); // Check every 500ms
      });
    }

    findCompanyName();
  }

  // Wait for the DOM to be fully loaded before executing
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    createPopup();
  } else {
    document.addEventListener('DOMContentLoaded', createPopup);
  }
})();
